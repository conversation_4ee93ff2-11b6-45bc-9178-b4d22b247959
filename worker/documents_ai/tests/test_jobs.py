import pytest

from app.documents_ai.enums import StructuredDataExtractionStatus as SDAStatus
from app.tests.common import prepare_client, prepare_document_data

from .conftest import FakeBedrockClient
from .helpers import (
    assert_bbox_enrichment,
    assert_extraction_status,
    create_bedrock_response_with_indexes,
    create_textract_response_with_bboxes,
    run_extraction_job,
    setup_extraction_test_data,
)


async def test_extraction_job_invalid_structured_data(
    aiohttp_client, mock_document_bytes_downloader, fake_version_id
):
    """Test extraction job with invalid structured data that should fail validation."""
    app, _client, user = await prepare_client(aiohttp_client)
    document = await prepare_document_data(app, owner=user, title='test')

    # Setup invalid Bedrock response (has required fields but invalid content)
    invalid_response = {
        'content': [{'text': '{"invalid": "structured_data"}'}],
        'usage': {'input_tokens': 1, 'output_tokens': 1},
    }
    invalid_bedrock_client = FakeBedrockClient(invalid_response)

    # Setup services context
    from app.services import services as _svc

    from .conftest import FakeTextractClient

    textract_client = FakeTextractClient(
        {
            'Blocks': [
                {
                    'BlockType': 'LINE',
                    'Text': 'hello',
                    'Geometry': {
                        'BoundingBox': {'Left': 0.1, 'Top': 0.2, 'Width': 0.3, 'Height': 0.1}
                    },
                }
            ]
        }
    )
    _svc.ctx_textract_client.set(textract_client)
    _svc.ctx_bedrock_client.set(invalid_bedrock_client)

    # Setup database
    await setup_extraction_test_data(document.id, user.company_id)

    # Run extraction job (should fail due to invalid response)
    import pydantic

    with pytest.raises(pydantic.ValidationError):
        await run_extraction_job(app, document.id, fake_version_id, user.company_id)

    # Verify status is ERROR
    await assert_extraction_status(document.id, user.company_id, SDAStatus.ERROR)


async def test_extraction_job_execution(
    aiohttp_client, mock_aws_services, mock_document_bytes_downloader, fake_version_id
):
    """Test successful extraction job execution."""
    app, _client, user = await prepare_client(aiohttp_client)
    document = await prepare_document_data(app, owner=user, title='test')

    # AWS services are already set up by the fixture

    # Setup database
    await setup_extraction_test_data(document.id, user.company_id)

    # Run extraction job
    await run_extraction_job(app, document.id, fake_version_id, user.company_id)

    # Assert status updated to AWAITING_VALIDATION
    await assert_extraction_status(document.id, user.company_id, SDAStatus.AWAITING_VALIDATION)


async def test_bboxes_from_indexes(aiohttp_client, mock_document_bytes_downloader, fake_version_id):
    """Test that bounding boxes are correctly enriched from OCR indexes."""
    app, _client, user = await prepare_client(aiohttp_client)
    document = await prepare_document_data(app, owner=user, title='bbox')

    # Setup specific responses for bbox testing
    textract_response = create_textract_response_with_bboxes()
    bedrock_response = create_bedrock_response_with_indexes()

    # Create specialized clients for this test
    from app.services import services as _svc

    from .conftest import FakeBedrockClient, FakeTextractClient

    textract_client = FakeTextractClient(textract_response)
    bedrock_client = FakeBedrockClient(bedrock_response)

    _svc.ctx_textract_client.set(textract_client)
    _svc.ctx_bedrock_client.set(bedrock_client)

    # Setup database
    await setup_extraction_test_data(document.id, user.company_id)

    # Run extraction job
    await run_extraction_job(app, document.id, fake_version_id, user.company_id)

    # Verify status
    await assert_extraction_status(document.id, user.company_id, SDAStatus.AWAITING_VALIDATION)

    # Test bbox enrichment functionality directly
    from app.documents_ai.types import OCRProcessedDataItem
    from app.documents_ai.utils import _prepare_output

    # Create OCR data items for testing
    ocr_data = [
        OCRProcessedDataItem(text='first', page=0, bbox=[100, 200, 400, 300]),
        OCRProcessedDataItem(text='second', page=1, bbox=[0, 0, 100, 200]),
    ]

    # Test the enrichment
    test_data = {
        'details': {
            'number': {'value': 'A-1', 'indexes': [0]},
            'date_created': {'value': '2024-05-05', 'indexes': [1]},
        }
    }

    enriched = _prepare_output(test_data, ocr_data)
    assert_bbox_enrichment(enriched)
