from unittest import mock

import ujson

import worker.documents_ai.jobs as worker_jobs
from app.documents_ai.db import insert_or_replace_extractions, select_extractions_for_documents
from app.documents_ai.enums import StructuredDataExtractionStatus as SDAStatus
from app.services import services
from app.tests.common import prepare_client, prepare_document_data
from worker.documents_ai.jobs import process_structured_data_extraction_job


async def test_extraction_job_invalid_structured_data(aiohttp_client, monkeypatch):
    app, _client, user = await prepare_client(aiohttp_client)
    document = await prepare_document_data(app, owner=user, title='test')

    # Prepare fake version and content
    version_id = '00000000-0000-4000-8000-000000000001'
    fake_bytes = b'%PDF-1.4...'

    async def _fake_get_bytes(*, document_id: str, version_id: str | None = None) -> bytes | None:
        assert document_id == document.id
        return fake_bytes

    monkeypatch.setattr(worker_jobs, 'get_document_bytes_for_extraction', _fake_get_bytes)

    # Mock Textract to return some OCR
    fake_textract_resp = {'Blocks': [{'BlockType': 'LINE', 'Text': 'hello'}]}

    async def _fake_detect_document_text(**kwargs):
        assert kwargs['Document']['Bytes'] == fake_bytes
        return fake_textract_resp

    class _AsyncBody:
        async def __aenter__(self):
            return self

        async def __aexit__(self, exc_type, exc, tb):
            return False

        async def read(self) -> bytes:
            # Return non-conformant payload that will fail StructuredDataModel
            return ujson.dumps({'unexpected': 'format'}).encode()

    async def _fake_invoke_model(**kwargs):
        return {'body': _AsyncBody()}

    # Ensure clients exist
    if services.textract_client is None:

        class _FakeTextract:
            async def detect_document_text(self, **kwargs):
                return await _fake_detect_document_text(**kwargs)

        fake_textract = _FakeTextract()
        from app.services import services as _svc

        _svc.ctx_textract_client.set(fake_textract)
        app['textract_client'] = fake_textract

    if services.bedrock_client is None:

        class _FakeBedrock:
            async def invoke_model(self, **kwargs):
                return await _fake_invoke_model(**kwargs)

        fake_bedrock = _FakeBedrock()
        from app.services import services as _svc

        _svc.ctx_bedrock_client.set(fake_bedrock)
        app['bedrock_client'] = fake_bedrock

    monkeypatch.setattr(
        services.textract_client, 'detect_document_text', _fake_detect_document_text
    )
    monkeypatch.setattr(services.bedrock_client, 'invoke_model', _fake_invoke_model)

    # Insert or reuse extraction row
    async with services.db.acquire() as conn:
        await insert_or_replace_extractions(conn, document_id=document.id)

    await process_structured_data_extraction_job(
        app=app,
        data={'document_id': document.id, 'version_id': version_id},
        logger=mock.Mock(),
    )

    # Verify status is ERROR
    async with services.db.acquire() as conn:
        row = await select_extractions_for_documents(conn, document_id=document.id)
        assert row is not None
        assert row.status == SDAStatus.ERROR


async def test_extraction_job_execution(aiohttp_client, monkeypatch):
    app, _client, user = await prepare_client(aiohttp_client)
    document = await prepare_document_data(app, owner=user, title='test')

    # Prepare a fake version id and content
    version_id = '00000000-0000-4000-8000-000000000001'
    fake_bytes = b'%PDF-1.4...'

    # Mock downloader to return our bytes
    async def _fake_get_bytes(*, document_id: str, version_id: str | None = None) -> bytes | None:
        assert document_id == document.id
        return fake_bytes

    monkeypatch.setattr(worker_jobs, 'get_document_bytes_for_extraction', _fake_get_bytes)

    # Mock Textract and Bedrock
    fake_textract_resp = {'Blocks': [{'BlockType': 'LINE', 'Text': 'hello'}]}
    fake_bedrock_payload = {
        'content': [{'text': ujson.dumps({'fields': {'ok': True}})}],
        'usage': {'input_tokens': 1, 'output_tokens': 1},
    }

    async def _fake_detect_document_text(**kwargs):
        assert kwargs['Document']['Bytes'] == fake_bytes
        return fake_textract_resp

    class _AsyncBody:
        def __init__(self, data: bytes) -> None:
            self._data = data

        async def __aenter__(self):
            return self

        async def __aexit__(self, exc_type, exc, tb):
            return False

        async def read(self) -> bytes:
            return ujson.dumps(fake_bedrock_payload).encode()

    async def _fake_invoke_model(**kwargs):
        return {'body': _AsyncBody(b'')}

    # Ensure clients exist in test env; create fakes if not configured
    if services.textract_client is None:

        class _FakeTextract:
            async def detect_document_text(self, **kwargs):
                return await _fake_detect_document_text(**kwargs)

        fake_textract = _FakeTextract()
        from app.services import services as _svc

        _svc.ctx_textract_client.set(fake_textract)
        app['textract_client'] = fake_textract

    if services.bedrock_client is None:

        class _FakeBedrock:
            async def invoke_model(self, **kwargs):
                return await _fake_invoke_model(**kwargs)

        fake_bedrock = _FakeBedrock()
        from app.services import services as _svc

        _svc.ctx_bedrock_client.set(fake_bedrock)
        app['bedrock_client'] = fake_bedrock

    # Monkeypatch methods to control responses
    monkeypatch.setattr(
        services.textract_client, 'detect_document_text', _fake_detect_document_text
    )
    monkeypatch.setattr(services.bedrock_client, 'invoke_model', _fake_invoke_model)

    async with services.db.acquire() as conn:
        await insert_or_replace_extractions(conn, document_id=document.id)

    await process_structured_data_extraction_job(
        app,
        data={'document_id': document.id, 'version_id': version_id},
        logger=mock.Mock(),
    )

    # Assert status updated
    async with services.db.acquire() as conn:
        row = await select_extractions_for_documents(conn, document_id=document.id)
        assert row is not None
        assert row.status == SDAStatus.AWAITING_VALIDATION


async def test_bboxes_from_indexes(aiohttp_client, monkeypatch):
    app, _client, user = await prepare_client(aiohttp_client)
    document = await prepare_document_data(app, owner=user, title='bbox')

    version_id = '00000000-0000-4000-8000-000000000001'
    fake_bytes = b'%PDF-1.4...'

    async def _fake_get_bytes(*, document_id: str, version_id: str | None = None) -> bytes | None:
        assert document_id == document.id
        return fake_bytes

    monkeypatch.setattr(worker_jobs, 'get_document_bytes_for_extraction', _fake_get_bytes)

    # Textract returns two LINE blocks with specific bounding boxes
    fake_textract_resp = {
        'Blocks': [
            {
                'BlockType': 'LINE',
                'Text': 'first',
                'Page': 1,
                'Geometry': {'BoundingBox': {'Left': 0.1, 'Top': 0.2, 'Width': 0.3, 'Height': 0.1}},
            },
            {
                'BlockType': 'LINE',
                'Text': 'second',
                'Page': 2,
                'Geometry': {'BoundingBox': {'Left': 0.0, 'Top': 0.0, 'Width': 0.1, 'Height': 0.2}},
            },
        ]
    }

    # Model returns PoC shape referencing indexes 0 and 1
    fake_bedrock_payload = {
        'content': [
            {
                'text': ujson.dumps(
                    {
                        'extracted_data': {
                            'details': {
                                'number': {'value': 'A-1', 'indexes': [0]},
                                'date_created': {'value': '2024-05-05', 'indexes': [1]},
                            }
                        }
                    }
                )
            }
        ],
        'usage': {'input_tokens': 1, 'output_tokens': 1},
    }

    async def _fake_detect_document_text(**kwargs):
        assert kwargs['Document']['Bytes'] == fake_bytes
        return fake_textract_resp

    class _AsyncBody:
        def __init__(self, data: bytes) -> None:
            self._data = data

        async def __aenter__(self):
            return self

        async def __aexit__(self, exc_type, exc, tb):
            return False

        async def read(self) -> bytes:
            return ujson.dumps(fake_bedrock_payload).encode()

    async def _fake_invoke_model(**kwargs):
        return {'body': _AsyncBody(b'')}

    # Ensure clients exist in test env
    if services.textract_client is None:

        class _FakeTextract:
            async def detect_document_text(self, **kwargs):
                return await _fake_detect_document_text(**kwargs)

        fake_textract = _FakeTextract()
        from app.services import services as _svc

        _svc.ctx_textract_client.set(fake_textract)
        app['textract_client'] = fake_textract

    if services.bedrock_client is None:

        class _FakeBedrock:
            async def invoke_model(self, **kwargs):
                return await _fake_invoke_model(**kwargs)

        fake_bedrock = _FakeBedrock()
        from app.services import services as _svc

        _svc.ctx_bedrock_client.set(fake_bedrock)
        app['bedrock_client'] = fake_bedrock

    monkeypatch.setattr(
        services.textract_client, 'detect_document_text', _fake_detect_document_text
    )
    monkeypatch.setattr(services.bedrock_client, 'invoke_model', _fake_invoke_model)

    async with services.db.acquire() as conn:
        await insert_or_replace_extractions(conn, document_id=document.id)

    await process_structured_data_extraction_job(
        app,
        data={'document_id': document.id, 'version_id': version_id},
        logger=mock.Mock(),
    )

    # Verify status and that bboxes are present with expected pages
    async with services.db.acquire() as conn:
        row = await select_extractions_for_documents(conn, document_id=document.id)
        assert row is not None
        assert row.status == SDAStatus.AWAITING_VALIDATION

    # We rely on jobs pipeline saving result to S3; instead we check enrich behavior
    # by reusing internals with provided OCR and model output
    enriched = worker_jobs._prepare_output_with_bboxes(
        {
            'details': {
                'number': {'value': 'A-1', 'indexes': [0]},
                'date_created': {'value': '2024-05-05', 'indexes': [1]},
            }
        },
        fake_textract_resp
        | {'PagesInfo': {1: {'width': 1000, 'height': 1000}, 2: {'width': 1000, 'height': 1000}}},
    )
    assert 'bboxes' in enriched['details']['number']
    assert enriched['details']['number']['bboxes'][0]['page'] == 1
    assert enriched['details']['date_created']['bboxes'][0]['page'] == 2
