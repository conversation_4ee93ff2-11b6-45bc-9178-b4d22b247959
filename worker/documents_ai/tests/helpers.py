"""Helper functions for documents_ai worker tests."""

from unittest import mock

import ujson

from app.documents_ai.db import insert_or_replace_extractions, select_extractions_for_documents
from app.documents_ai.enums import StructuredDataExtractionStatus as SDAStatus
from app.services import services
from worker.documents_ai.jobs import process_structured_data_extraction_job


async def setup_extraction_test_data(document_id: str, company_id: str) -> None:
    """Setup initial extraction data in database."""
    from app.documents_ai.enums import StructuredDataExtractionStatus as SDAStatus
    from app.documents_ai.types import DocumentDataExtraction

    extraction_data = DocumentDataExtraction(
        document_id=document_id,
        company_id=company_id,
        status=SDAStatus.PENDING,
        error_message=None,
    )

    async with services.db.acquire() as conn:
        await insert_or_replace_extractions(conn, company_id=company_id, data=[extraction_data])


async def assert_extraction_status(
    document_id: str, company_id: str, expected_status: SDAStatus
) -> None:
    """Assert that extraction has the expected status."""
    async with services.db.acquire() as conn:
        rows = await select_extractions_for_documents(
            conn, company_id=company_id, document_ids=[document_id]
        )
        assert len(rows) == 1
        assert rows[0].status == expected_status


async def run_extraction_job(app, document_id: str, version_id: str, company_id: str) -> None:
    """Run the extraction job with standard parameters."""
    await process_structured_data_extraction_job(
        app=app,
        data={'document_id': document_id, 'version_id': version_id, 'company_id': company_id},
        logger=mock.Mock(),
    )


def create_textract_response_with_bboxes() -> dict:
    """Create a Textract response with specific bounding boxes for testing."""
    return {
        'Blocks': [
            {
                'BlockType': 'LINE',
                'Text': 'first',
                'Page': 1,
                'Geometry': {'BoundingBox': {'Left': 0.1, 'Top': 0.2, 'Width': 0.3, 'Height': 0.1}},
            },
            {
                'BlockType': 'LINE',
                'Text': 'second',
                'Page': 2,
                'Geometry': {'BoundingBox': {'Left': 0.0, 'Top': 0.0, 'Width': 0.1, 'Height': 0.2}},
            },
        ]
    }


def create_bedrock_response_with_indexes() -> dict:
    """Create a Bedrock response that references OCR indexes for bbox testing."""
    return {
        'content': [
            {
                'text': ujson.dumps(
                    {
                        'extracted_data': {
                            'details': {
                                'number': {'value': 'A-1', 'indexes': [0]},
                                'date_created': {'value': '2024-05-05', 'indexes': [1]},
                            }
                        }
                    }
                )
            }
        ],
        'usage': {'input_tokens': 1, 'output_tokens': 1},
    }


def create_invalid_bedrock_response() -> dict:
    """Create an invalid Bedrock response for error testing."""
    return {'unexpected': 'format'}


def assert_bbox_enrichment(enriched_data: dict) -> None:
    """Assert that bbox enrichment worked correctly."""
    assert 'bboxes' in enriched_data['details']['number']
    assert enriched_data['details']['number']['bboxes'][0]['page'] == 0
    assert enriched_data['details']['date_created']['bboxes'][0]['page'] == 1
