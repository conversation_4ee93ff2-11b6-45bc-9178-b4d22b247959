"""Shared fixtures and utilities for documents_ai worker tests."""

import pytest
import ujson
from unittest import mock

from app.services import services


# Test constants
TEST_VERSION_ID = '00000000-0000-4000-8000-000000000001'
# Minimal valid PDF content for testing
TEST_DOCUMENT_BYTES = b"""%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj
2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj
3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
>>
endobj
xref
0 4
0000000000 65535 f
0000000009 00000 n
0000000058 00000 n
0000000115 00000 n
trailer
<<
/Size 4
/Root 1 0 R
>>
startxref
181
%%EOF"""


@pytest.fixture
def fake_document_bytes():
    """Fake document content for testing."""
    return TEST_DOCUMENT_BYTES


@pytest.fixture
def fake_version_id():
    """Fake version ID for testing."""
    return TEST_VERSION_ID


@pytest.fixture
def fake_textract_response():
    """Basic Textract OCR response for testing."""
    return {
        'Blocks': [
            {
                'BlockType': 'LINE',
                'Text': 'hello',
                'Geometry': {
                    'BoundingBox': {
                        'Left': 0.1,
                        'Top': 0.2,
                        'Width': 0.3,
                        'Height': 0.1
                    }
                }
            }
        ]
    }


@pytest.fixture
def fake_bedrock_success_response():
    """Successful Bedrock response for testing."""
    return {
        'content': [{'text': ujson.dumps({'fields': {'ok': True}})}],
        'usage': {'input_tokens': 1, 'output_tokens': 1},
    }


@pytest.fixture
def fake_bedrock_invalid_response():
    """Invalid Bedrock response that should cause validation errors."""
    return {'unexpected': 'format'}


class AsyncBodyMock:
    """Mock for aiohttp response body with async context manager."""

    def __init__(self, data: bytes):
        self._data = data

    async def __aenter__(self):
        return self

    async def __aexit__(self, exc_type, exc, tb):
        return False

    async def read(self) -> bytes:
        return self._data


class FakeTextractClient:
    """Mock Textract client for testing."""

    def __init__(self, response_data):
        self.response_data = response_data

    async def detect_document_text(self, **kwargs):
        return self.response_data


class FakeBedrockClient:
    """Mock Bedrock client for testing."""

    def __init__(self, response_data):
        self.response_data = response_data

    async def invoke_model(self, **kwargs):
        body_data = ujson.dumps(self.response_data).encode()
        return {'body': AsyncBodyMock(body_data)}


@pytest.fixture
def mock_textract_client(fake_textract_response):
    """Mock Textract client fixture."""
    return FakeTextractClient(fake_textract_response)


@pytest.fixture
def mock_bedrock_client(fake_bedrock_success_response):
    """Mock Bedrock client fixture."""
    return FakeBedrockClient(fake_bedrock_success_response)


@pytest.fixture
def mock_aws_services(mock_textract_client, mock_bedrock_client):
    """Setup mock AWS services for testing."""
    from app.services import services as _svc

    # Set context variables for the clients
    _svc.ctx_textract_client.set(mock_textract_client)
    _svc.ctx_bedrock_client.set(mock_bedrock_client)

    return mock_textract_client, mock_bedrock_client


@pytest.fixture
def mock_document_bytes_downloader(monkeypatch, fake_document_bytes):
    """Mock the document bytes downloader function."""
    from app.documents_ai import utils as ai_utils

    async def _fake_get_bytes(*, document_id: str, version_id: str | None = None) -> bytes | None:
        return fake_document_bytes

    monkeypatch.setattr(ai_utils, 'get_document_bytes_for_extraction', _fake_get_bytes)
    return _fake_get_bytes
