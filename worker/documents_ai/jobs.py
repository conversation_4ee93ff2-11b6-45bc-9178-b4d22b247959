import logging

import pydantic
from aiohttp import web

from app.documents_ai.db import update_extraction_status
from app.documents_ai.enums import (
    StructuredDataErrorKey,
)
from app.documents_ai.enums import (
    StructuredDataExtractionStatus as SDAStatus,
)
from app.documents_ai.utils import (
    extract_structured_data_with_bedrock,
    get_document_structured_data_s3_key,
    process_document_with_ocr,
)
from app.lib import s3_utils
from app.lib.types import DataDict
from app.services import services
from worker.utils import retry_config


@retry_config(max_attempts=20, delay_minutes=5)
async def process_structured_data_extraction_job(
    __: web.Application, data: DataDict, logger: logging.Logger
) -> None:
    """
    Job payload expects:
      - document_id: str
      - version_id: str | None
      - company_id: str
    """
    document_id: str = data['document_id']
    version_id: str | None = data.get('version_id')
    company_id: str = data['company_id']

    async with services.db.acquire() as conn:
        await update_extraction_status(
            conn, company_id=company_id, document_id=document_id, status=SDAStatus.IN_PROGRESS
        )

    try:
        if await s3_utils.exists(get_document_structured_data_s3_key(document_id)):
            logger.info(
                'Structured data found on S3, skipping extraction',
                extra={'document_id': document_id},
            )
            async with services.db.acquire() as conn:
                await update_extraction_status(
                    conn,
                    document_id=document_id,
                    status=SDAStatus.AWAITING_VALIDATION,
                    company_id=company_id,
                )
            return

        # OCR with Textract
        ocr_data = await process_document_with_ocr(document_id=document_id, version_id=version_id)

        # Structured data extraction with Bedrock
        await extract_structured_data_with_bedrock(ocr_data=ocr_data, document_id=document_id)

    except (pydantic.ValidationError, ValueError):
        logger.warning(
            'Structured data parsing/validation failed', extra={'document_id': document_id}
        )
        async with services.db.acquire() as conn:
            await update_extraction_status(
                conn,
                document_id=document_id,
                company_id=company_id,
                status=SDAStatus.ERROR,
                error_message=StructuredDataErrorKey.INVALID_STRUCTURED_DATA,
            )
        raise
    except Exception:
        logger.warning('Extraction job failed', extra={'document_id': document_id})
        async with services.db.acquire() as conn:
            await update_extraction_status(
                conn,
                company_id=company_id,
                document_id=document_id,
                status=SDAStatus.ERROR,
                error_message=StructuredDataErrorKey.UNKNOWN_ERROR,
            )
        raise

    # Successfully extracted structured_data - Mark AWAITING_VALIDATION
    async with services.db.acquire() as conn:
        await update_extraction_status(
            conn,
            document_id=document_id,
            status=SDAStatus.AWAITING_VALIDATION,
            company_id=company_id,
        )
