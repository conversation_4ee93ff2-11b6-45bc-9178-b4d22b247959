import datetime
from collections import defaultdict
from collections.abc import Awaitable, Callable, Iterable, Mapping
from typing import (
    TYPE_CHECKING,
    Any,
)

from aiohttp import web
from aiohttp.web_request import FileField
from aiokafka.consumer.fetcher import ConsumerRecord
from aiokafka.structs import TopicPartition
from multidict import MultiDict, MultiDictProxy
from redis.asyncio.client import Pipeline as BasePipeline
from redis.asyncio.client import Redis as BaseRedis

if TYPE_CHECKING:
    Redis = BaseRedis[str]
    Pipeline = BasePipeline[str]
    from app.auth.types import AuthUser, BaseUser, User
else:
    Redis = BaseRedis
    Pipeline = BasePipeline

type AnyAwaitable = Awaitable[Any]
type AnyDict = dict[Any, Any]
type AnyList = list[Any]
type AnyMapping = Mapping[Any, Any]
type DataDict = dict[str, Any]
type DataMapping = Mapping[str, Any]
type NestedDefaultDict = defaultdict[str, defaultdict[str, Any]]
type IntList = list[int]
type OptionalDate = datetime.date | None
type OptionalDateTime = datetime.datetime | None
type OptionalDateOrDateTime = OptionalDate | OptionalDateTime
type StrDict = dict[str, str]
type StrIterable = Iterable[str]
type StrList = list[str]
type StrMapping = Mapping[str, str]

type MultipartFormValue = str | bytes | bytearray | FileField
type MultipartFormData = MultiDict[MultipartFormValue]
type MultipartFormDataProxy = MultiDictProxy[MultipartFormValue]

type ConsumedChunk = dict[TopicPartition, list[ConsumerRecord]]

type HandlerResponse = web.StreamResponse  # web.Request inherited from web.StreamResponse
type Handler = Callable[[web.Request], Awaitable[HandlerResponse]]
type UserHandler = Callable[[web.Request, User], Awaitable[HandlerResponse]]
type BaseUserHandler = Callable[[web.Request, BaseUser | User], Awaitable[HandlerResponse]]
type SignSessionHandler = Callable[[web.Request, AuthUser | User], Awaitable[HandlerResponse]]
type AnyUserHandler = Callable[
    [web.Request, AuthUser | BaseUser | User], Awaitable[HandlerResponse]
]
type HandlerDecorator = Callable[[Handler], Handler]

type Function = Callable[..., Any]
