"""document-structured-data-table

Revision ID: e05d9ebcfa76
Revises: a9523a1d0626
Create Date: 2025-08-28 12:41:35.232581

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from app.models.types import SoftEnum
from app.documents_ai.enums import StructuredDataExtractionStatus as SDAStatus

# revision identifiers, used by Alembic.
revision = 'e05d9ebcfa76'
down_revision = 'a9523a1d0626'
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        'document_structured_data',
        sa.Column(
            'id', postgresql.UUID(), server_default=sa.text('uuid_generate_v4()'), nullable=False
        ),
        sa.Column('document_id', sa.String(length=64), nullable=False),
        sa.Column('company_id', postgresql.UUID(), nullable=False),
        sa.Column(
            'status', SoftEnum(SDAStatus), server_default=SDAStatus.PENDING.value, nullable=False
        ),
        sa.Column('error_message', sa.Text(), nullable=True),
        sa.Column(
            'date_created',
            sa.DateTime(timezone=True),
            server_default=sa.text('now()'),
            nullable=False,
        ),
        sa.Column(
            'date_updated',
            sa.DateTime(timezone=True),
            server_default=sa.text('now()'),
            nullable=False,
        ),
        sa.ForeignKeyConstraint(['company_id'], ['companies.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['document_id'], ['documents.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id'),
    )
    op.create_index(
        op.f('ix_document_structured_data_company_id'),
        'document_structured_data',
        ['company_id'],
        unique=False,
    )
    op.create_index(
        op.f('ix_document_structured_data_document_id'),
        'document_structured_data',
        ['document_id'],
        unique=False,
    )
    op.create_index(
        'ix_document_structured_data_document_id_company_id',
        'document_structured_data',
        ['company_id', 'document_id'],
        unique=True,
    )


def downgrade():
    op.drop_index(
        'ix_document_structured_data_document_id_company_id', table_name='document_structured_data'
    )
    op.drop_index(
        op.f('ix_document_structured_data_document_id'), table_name='document_structured_data'
    )
    op.drop_index(
        op.f('ix_document_structured_data_company_id'), table_name='document_structured_data'
    )
    op.drop_table('document_structured_data')
