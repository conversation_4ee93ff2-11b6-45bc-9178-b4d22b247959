from http import HTTPStatus
from unittest import mock

import pytest
import ujson
from aiohttp import FormData

from app.document_categories.db import insert_document_category
from app.document_categories.types import PublicDocumentCategory
from app.documents_ai import utils
from app.documents_ai.db import select_extractions_for_documents
from app.documents_ai.enums import StructuredDataExtractionStatus
from app.services import services
from app.tests.common import (
    AsyncBytesIO,
    prepare_auth_headers,
    prepare_client,
    prepare_document_data,
    prepare_public_document_categories,
    set_company_config,
)

TEST_EDRPOU_RECIPIENT = '12344321'


def _patch_bedrock_client(monkeypatch, document_category=PublicDocumentCategory.contract.value):
    _mocked_response = {
        'content': [
            {
                'text': ujson.dumps(
                    {
                        # Append document category to the AI response
                        'category': document_category,
                        'number': 'ЕДО - 13722',
                        'date': '2024-05-17',
                        'amount': 123.23,
                        'edrpou': TEST_EDRPOU_RECIPIENT,
                    }
                )
            }
        ],
        'usage': {
            'input_tokens': 10,
            'output_tokens': 10,
        },
    }

    # mock bedrock-runtime client
    fake_model_response = {'body': AsyncBytesIO(ujson.dumps(_mocked_response).encode())}
    fake_invoke_model = mock.AsyncMock(return_value=fake_model_response)
    monkeypatch.setattr(utils.services.bedrock_client, 'invoke_model', fake_invoke_model)

    # mock pdf text extraction
    monkeypatch.setattr(
        utils,
        '_extract_text_from_document_content_sync',
        mock.Mock(return_value='test doc content'),
    )


@pytest.mark.parametrize(
    'feature_enabled, expected_status',
    [
        (True, HTTPStatus.OK),
        (False, HTTPStatus.FORBIDDEN),
    ],
)
async def test_suggest_by_doc_content(
    aiohttp_client, monkeypatch, feature_enabled, expected_status
):
    """
    General test for happy path for document AI suggest
    Check internal API for frontend by document content
    """

    app, client, user = await prepare_client(aiohttp_client)
    await set_company_config(
        app,
        company_id=user.company_id,
        allow_suggesting_document_meta_with_ai=feature_enabled,
    )
    _patch_bedrock_client(monkeypatch)
    await prepare_public_document_categories(amount=5)

    data = FormData()
    data.add_field('file', b'content', filename='test.pdf')

    resp = await client.post(
        '/internal-api/documents/suggest',
        headers=prepare_auth_headers(user),
        data=data,
    )
    assert resp.status == expected_status
    resp_json = await resp.json()

    if feature_enabled:
        assert sorted(
            [
                'title',
                'category',
                'date',
                'number',
                'amount',
                'companyEdrpou',
                'companyName',
                'companyEmail',
            ]
        ) == sorted(resp_json.keys())
        assert resp_json['title'] == 'test'
        assert resp_json['number'] == 'ЕДО - 13722'
        assert resp_json['amount'] == '123.23'
        assert resp_json['companyEdrpou'] == TEST_EDRPOU_RECIPIENT
        assert resp_json['category'] == PublicDocumentCategory.contract.value


async def test_suggest_by_doc_id(aiohttp_client, monkeypatch):
    """
    General test for happy path for document AI suggest
    Check internal API for frontend by document_id
    """

    app, client, user = await prepare_client(aiohttp_client)
    document = await prepare_document_data(app, owner=user, title='test')
    _patch_bedrock_client(monkeypatch)
    await prepare_public_document_categories(amount=5)

    resp = await client.post(
        f'/internal-api/documents/{document.id}/suggest',
        headers=prepare_auth_headers(user),
    )
    assert resp.status == HTTPStatus.OK
    resp_json = await resp.json()

    assert sorted(
        [
            'title',
            'category',
            'date',
            'number',
            'amount',
            'companyEdrpou',
            'companyName',
            'companyEmail',
        ]
    ) == sorted(resp_json.keys())
    assert resp_json['title'] == 'test'
    assert resp_json['number'] == 'ЕДО - 13722'
    assert resp_json['amount'] == '123.23'
    assert resp_json['companyEdrpou'] == TEST_EDRPOU_RECIPIENT
    assert resp_json['category'] == PublicDocumentCategory.contract.value


async def test_internal_document_category_was_suggested(aiohttp_client, monkeypatch):
    app, client, user = await prepare_client(aiohttp_client)
    await set_company_config(
        app,
        company_id=user.company_id,
        allow_suggesting_document_meta_with_ai=True,
    )

    # Prepare document category
    async with services.db.acquire() as conn:
        document_category = await insert_document_category(
            conn=conn,
            title='INTERNAL DOCUMENT CATEGORY',
            company_id=user.company_id,
        )

    _patch_bedrock_client(monkeypatch, document_category.id)

    data = FormData()
    data.add_field('file', b'content', filename='test.pdf')

    resp = await client.post(
        '/internal-api/documents/suggest',
        headers=prepare_auth_headers(user),
        data=data,
    )
    assert resp.status == HTTPStatus.OK
    resp_json = await resp.json()

    assert sorted(
        [
            'title',
            'category',
            'date',
            'number',
            'amount',
            'companyEdrpou',
            'companyName',
            'companyEmail',
        ]
    ) == sorted(resp_json.keys())
    assert resp_json['title'] == 'test'
    assert resp_json['number'] == 'ЕДО - 13722'
    assert resp_json['amount'] == '123.23'
    assert resp_json['companyEdrpou'] == TEST_EDRPOU_RECIPIENT
    assert resp_json['category'] == document_category.id


async def test_start_extraction_view(aiohttp_client, monkeypatch):
    app, client, user = await prepare_client(aiohttp_client)
    document = await prepare_document_data(app, owner=user, title='test')

    # Capture published kafka records
    published = {}

    async def _fake_send_records(*, topic, values):
        published['topic'] = topic
        published['values'] = values

    monkeypatch.setattr(services.kafka, 'send_records', _fake_send_records)

    resp = await client.post(
        '/internal-api/documents/ai/extract-data',
        json={'document_ids': [document.id]},
        headers=prepare_auth_headers(user),
    )
    assert resp.status == HTTPStatus.OK
    body = await resp.json()
    assert body['data'] and body['data'][0]['document_id'] == document.id

    # After request, DB should have a PENDING row and kafka published
    async with services.db.acquire() as conn:
        rows = await select_extractions_for_documents(
            conn, company_id=user.company_id, document_ids=[document.id]
        )
        assert len(rows) == 1
        assert rows[0].status == StructuredDataExtractionStatus.PENDING.value

    assert 'values' in published
    assert published['values'][0]['document_id'] == document.id


async def test_get_extractions_status(aiohttp_client, monkeypatch):
    app, client, user = await prepare_client(aiohttp_client)
    document = await prepare_document_data(app, owner=user, title='test')

    # avoid extraction task calls
    monkeypatch.setattr(services.kafka, 'send_records', mock.AsyncMock())

    resp = await client.post(
        '/internal-api/documents/ai/extract-data',
        json={'document_ids': [document.id]},
        headers=prepare_auth_headers(user),
    )
    assert resp.status == HTTPStatus.OK
    body = await resp.json()
    assert body['data'] and body['data'][0]['document_id'] == document.id

    resp = await client.post(
        '/internal-api/documents/ai/extract-data-status',
        json={'document_ids': [document.id]},
        headers=prepare_auth_headers(user),
    )
    assert resp.status == HTTPStatus.OK
    body = await resp.json()

    assert body['data'] and body['data'][0]['document_id'] == document.id
    assert body['data'][0]['status'] == StructuredDataExtractionStatus.PENDING.value
