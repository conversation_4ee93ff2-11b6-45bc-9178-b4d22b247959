import pytest
import ujson

from app.documents_ai.utils import (
    _extract_text_from_document_content_sync,
    _prune_nones,
)

with open('app/documents_ai/tests/files/doc.pdf', 'rb') as f:
    pdf_content = f.read()
with open('app/documents_ai/tests/files/doc.docx', 'rb') as f:
    docx_content = f.read()
with open('app/documents_ai/tests/files/doc.doc', 'rb') as f:
    doc_content = f.read()

CONTENT_MAPPING = {
    '.pdf': pdf_content,
    '.docx': docx_content,
    '.doc': doc_content,
}


@pytest.mark.parametrize(
    'extension',
    ['.pdf', '.docx', '.doc'],
)
def test_extract_text_from_document_content_sync(extension, monkeypatch):
    monkeypatch.setattr('app.documents_ai.utils.MIN_TEXT_LEN', 0)

    content = CONTENT_MAPPING.get(extension)
    result = _extract_text_from_document_content_sync(content=content, extension=extension)
    assert result == 'Some text Of this Document Text with styles 42 wow!!! Ending!'


def test_extract_text_from_document_content_sync_min_text_len(monkeypatch):
    result = _extract_text_from_document_content_sync(content=pdf_content, extension='.pdf')
    assert result is None


def test_extract_text_from_document_content_sync_unsupported_extension():
    content = b'Unsupported content'
    extension = '.txt'
    result = _extract_text_from_document_content_sync(content=content, extension=extension)
    assert result is None


def test_prune_nones_basic():
    data = {
        'extracted_data': {
            'details': {
                'number': {'value': None, 'bboxes': [{'bbox': [0, 0, 1, 1], 'page': 1}]},
                'date_created': {'value': '2024-01-01', 'bboxes': []},
            },
            'items': [
                {'name': {'value': 'A', 'bboxes': [{'bbox': [1, 1, 2, 2], 'page': 2}]}},
                {'name': {'value': None}},
                {},
            ],
            'total_price': None,
        }
    }
    pruned = _prune_nones(ujson.loads(ujson.dumps(data)))
    # number removed, date_created kept but without empty bboxes, second item dropped
    assert pruned['extracted_data']['details'].get('number') is None
    assert 'bboxes' not in pruned['extracted_data']['details']['date_created']
    assert len(pruned['extracted_data']['items']) == 1
