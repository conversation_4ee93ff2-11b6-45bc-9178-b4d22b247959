import datetime
import typing as t
from dataclasses import dataclass, field

import ujson

from app.documents_ai.enums import StructuredDataErrorKey, StructuredDataExtractionStatus
from app.lib.datetime_utils import utc_now
from app.lib.types import DataDict


@dataclass
class DocumentDataExtraction:
    document_id: str
    status: StructuredDataExtractionStatus
    company_id: str
    error_message: StructuredDataErrorKey | None = None
    date_created: datetime.datetime = field(default_factory=utc_now)
    date_updated: datetime.datetime = field(default_factory=utc_now)

    @classmethod
    def from_row(cls, row: t.Any) -> t.Self:
        return cls(
            document_id=row.document_id,
            company_id=row.company_id,
            status=row.status,
            error_message=StructuredDataErrorKey(row.error_message) if row.error_message else None,
            date_created=row.date_created,
            date_updated=row.date_updated,
        )

    def to_db(self) -> DataDict:
        return {
            'document_id': self.document_id,
            'status': self.status,
            'company_id': self.company_id,
            'error_message': self.error_message.value if self.error_message else None,
        }


@dataclass(frozen=True)
class OCRProcessedDataItem:
    text: str
    page: int
    bbox: list[int]  # [x_min, y_min, x_max, y_max]


@dataclass(frozen=True)
class OCRPageInfo:
    number: int
    width: int
    height: int

    def to_dict(self) -> DataDict:
        return {
            'number': self.number,
            'width': self.width,
            'height': self.height,
        }


@dataclass(frozen=True)
class OCRDataRaw:
    pages_info: list[OCRPageInfo]

    # Contains a list of dicts, each dict is a response from Textract
    # for 1 page of a document.
    ocr_results: list[DataDict]

    @classmethod
    def from_dict(cls, data: DataDict) -> t.Self:
        return cls(
            pages_info=[OCRPageInfo(**page) for page in data['pages_info']],
            ocr_results=data['ocr_results'],
        )

    @classmethod
    def from_bytes(cls, data: bytes) -> t.Self:
        return cls.from_dict(ujson.loads(data.decode()))

    def to_dict(self) -> DataDict:
        return {
            'pages_info': [page.to_dict() for page in self.pages_info],
            'ocr_results': self.ocr_results,
        }
