MIN_TEXT_LEN = 100
TEXT_CHUNK_LEN = 1000

ALLOWED_AI_SUGGEST_EXTENSIONS = {'.pdf', '.doc', '.docx'}

PROMPT_TEMPLATE_SUGGEST_META = """
    Extract the following information from the document:

    Document content:
    {doc_text}

    1. category: int - one of the keys from category_map or null.
       category_map = {category_map}
    2. number: Document number as string or null.
    3. date: Document date (ISO 8601) or null.
    4. amount: Document amount as float or null.
    5. edrpou: ЄДРПОУ of the recipient
       (it must contains only digits or be a valid Ukraine passport number) or null,
       mention that document owner ЄДРПОУ is {owner_edrpou}, which differs from recipient one.

    Return the information as a JSON object without additional explanations.
"""

PROMPT_DOC_SUMMARY = """
Сформуй коротке резюме договору для керівника компанії. Виділи: сторон<PERSON>, предме<PERSON>, суму, строки,
ключові зобов'язання, ризики та відповідальність. Вказуй тільки важливе для прийняття рішення про
підписання. Пиши стисло, без юридичних формулювань, зрозуміло нефахівцю.
Відповідь має вкладатись у 1500 слів та мінімально відформатуватись у Markdown.
"""

EXTRACT_STRUCTURED_DATA_PROMPT = """
Ти асистент AI, який спеціалізується на витягуванні структурованої інформації з документів.

Твоє завдання – витягти структуровану інформацію з єдиного багатосторінкового документа.
Документ надано у вигляді OCR-результату у форматі `{index: text_line}` в порядку читання.

Вивід – єдиний JSON-об'єкт, що представляє весь документ і містить `extracted_data`:
витягнуті поля згідно зі схемою нижче.

Кожне кінцеве поле даних у `extracted_data` має бути присутнім у OCR вхідних даних
(допускається коригування) та представлятись об'єктом `fs`:
{
  "value": "float | string | null", // Значення поля
  "indexes": list[int]", // Список індексів із вхідних даних OCR, в яких було знайдено значення поля
} | null  // Якщо значення поля `value = null`,
повертати `null` замість всього обʼєкту `fs`.

Структура JSON виводу:
{
  "extracted_data": {
    "details": { // Реквізити акта
      "number": { /* fs (string) */ }, // Номер акта
      "date_created": { /* fs */ }, // Дата складання (ISO datetime)
      "reason": { /* fs (string) */ }  // Підстава (договір, рахунок, замовлення)
    },
    "parties_information": { // Інформація про сторони
      "customer": { // Замовник
        "company_name": { /* fs (string) */ }, // Найменування компанії/ФОП
        "edrpou": { /* fs (string) */ }, // Код ЄДРПОУ/ІПН
        "address": { /* fs (string) */ }, // Юридична адреса
        "bank_info": { // Банківські реквізити
          "mfo": { /* fs (string) */ }, // МФО
          "account": { /* fs (string) */ }, // Номер рахунку
          "bank_name": { /* fs (string) */ }, // Назва банку
          "iban": { /* fs (string) */ }, // IBAN
          "swift_code": { /* fs (string) */ }, // SWIFT код
          "edrpou": { /* fs (string) */ }, // ЄДРПОУ (якщо вказано)
          "ipn": { /* fs (string) */ }  // ІПН (якщо вказано)
        },
        "contact_info": { // Контактні дані
          "email": { /* fs (string) */ }, // Email
          "phone": { /* fs (string) */ }  // Телефон
        },
        "responsible_person": { // Особа, яка є представником замовника(customer),
                                   може бути вказаною в тексті документу
          "position": { /* fs (string) */ },  // Посада
          "full_name": { /* fs (string) */ }  // Повне ім'я
        }
      },
      "performer": {
        // Виконавець (всі поля аналогічно customer)
      }
    },
    "items": [ // Перелік робіт/послуг/товарів
      // Кожен об'єкт item містить поля зі стандартною структурою:
      // {
      //   "name": { /* fs (string) */ }, // Найменування
      //   "code": { /* fs (string) */ }, // Код
      //   "units": { /* fs (string) */ }, // Одиниця виміру
      //   "quantity": { /* fs (float) */ }, // Кількість
      //   "price": { /* fs (float) */ }, // Ціна за одиницю
      //   "total_price_without_vat": { /* fs (float) */ }, // Загальна вартість без ПДВ
      //   "total_price_with_vat": { /* fs (float) */ }, // Загальна вартість з ПДВ
      //   "deadline": { /* fs (ISO datetime | string) */ } // Термін виконання/надання
      // }, ...
      // Якщо в документів немає згадок про ПДВ, вважати що сума вказана із ПДВ
    ],
    "total_price": {
      // Загальна вартість без ПДВ по всім позиціям `items`
      "total_price_without_vat": { /* fs (float) */ },
      // Загальна сума з ПДВ по всім позиціям `items`
      "total_price_with_vat": { /* fs (float) */ },
      // Термін виконання/надання
      "overall_deadline": { /* fs (ISO datetime | string) */ }
    },
    "additional_data": { // Додаткові дані
      "purpose": { /* fs (string) */ }, // Призначення документа
      "contract_number": { /* fs (string) */ }, // Номер договору
      "contract_date": { /* fs (ISO datetime | string) */ }, // Дата договору
      "payment_purpose": { /* fs (string) */ }, // Призначення платежу
      "delivery_terms": { /* fs (string) */ }, // Умови постачання
    }
  }
}

Формат даних:
- IBAN: 29 символів, починається з UA, лише великі літери та цифри
- МФО: 6 цифр, унікальний ідентифікатор банку в Україні
- ЄДРПОУ: 8-10 цифр, унікальний ідентифікатор компанії в Україні
- ІПН: 10 цифр, унікальний ідентифікатор фізичної особи в Україні
- Номер рахунку: лише цифри, довжина від 14 до 20 цифр

Форматування адреси:
Цільовий формат - `[індекс], [область/місто обласного значення], [район], [населений пункт],
[вулиця/проспект/площа], [номер будинку], [корпус/офіс/квартира]`
Якщо якийсь компонент адреси відсутній у документі (наприклад, "район" для м. Києва
або номер офісу), потрібно його пропустити. Не додавай зайвих ком або плейсхолдерів.
Приклад: `"01001, м. Київ, вул. Хрещатик, буд. 22"`

Перед тим як повернути результат - відфільтруй усі null значення, фінальний обʼкт має
містити тільки не порожні значення / обʼєкти.
Поверни єдиний JSON об'єкт без додаткових пояснень або форматування.
"""
